defmodule CypridinaWeb.RacingLive.Index do
  use <PERSON><PERSON>ridina<PERSON>eb, :live_view
  alias RacingGame.RaceController
  alias Phoenix.PubSub
  require Logger

  @buy_limit_price 50

  def mount(_params, session, socket) do
    if connected?(socket) do
      # :timer.send_interval(@timer_interval, self(), :tick)

      # 订阅比赛更新
      PubSub.subscribe(Cypridina.PubSub, "race_updates")
      # 如果用户已登录，订阅用户消息
      if socket.assigns[:current_user] do
        PubSub.subscribe(Cypridina.PubSub, "user:#{socket.assigns.current_user.id}")
      end
    end

    # 从session中获取当前用户
    Logger.info("Session: #{inspect(session)}")

    socket =
      socket
      |> assign(:user_token, session["user_token"])

    socket =
      socket
      |> assign(:user_token, session["user_token"])

    current_user = socket.assigns[:current_user]

    # 使用新的用户资产系统获取积分
    points = Cypridina.Accounts.get_user_points(current_user.id)
    # 获取用户的持仓数据
    stocks =
      if current_user do
        RacingGame.Stock.get_user_stocks!(%{user_id: current_user.id})
        |> Enum.reduce(%{}, fn stock, acc ->
          Map.put(acc, stock.racer_id, stock.quantity)
        end)
      else
        %{}
      end

    # 获取最近5场比赛数据
    recent_races = fetch_recent_races()
    # 为动物添加序号，用于图表
    animals_with_index = add_index_to_animals(RacingGame.RaceController.animals())

    # 获取当前比赛信息
    current_race =
      case RacingGame.RaceController.get_current_game_info() do
        {:ok, race} -> race
        {:error, :no_race} -> nil
      end

    # 从数据库恢复当前比赛的用户下注数据
    my_bets =
      if current_user && current_race do
        get_user_current_race_bets(current_user.id, current_race.issue)
      else
        %{
          "A" => 0,
          "B" => 0,
          "C" => 0,
          "D" => 0,
          "E" => 0,
          "F" => 0
        }
      end

    {:ok,
     assign(socket,
       racing_game_url: Application.get_env(:cypridina, :racing_game)[:url],
       animals: animals_with_index,
       points: points,
       betting_enabled: true,
       show_bet_history: false,
       current_race: current_race,
       bet_history: [],
       race_status: nil,
       recent_races: recent_races,
       my_bets: my_bets,
       my_stocks: stocks,
       # 操作弹窗相关状态
       show_action_modal: false,
       show_liquidate_modal: false,
       current_action: nil,
       current_animal_id: nil,
       current_animal_name: nil,
       action_amount: 1,
       current_price: 0
     )}
  end

  # 处理统一的游戏数据更新事件
  def handle_info({:bet_won, %{bet: bet, payout: payout, commission: commission}}, socket) do
    commission_msg =
      if commission > 0 do
        "，抽水#{commission}积分"
      else
        ""
      end

    {:noreply,
     put_flash(socket, :info, "恭喜！您在#{bet.selection}上的竞猜赢得了#{payout}积分#{commission_msg}！（已扣除手续费）")}
  end

  def handle_info({:bet_lost, %{bet: bet}}, socket) do
    {:noreply, put_flash(socket, :info, "很遗憾，您在#{bet.selection}上的竞猜没有中奖。")}
  end

  def handle_info({:game_data_update, game_data}, socket) do
    case game_data.event_type do
      :new_race ->
        # 获取当前比赛信息
        case RaceController.get_current_game_info() do
          {:ok, game_info} ->
            Logger.info("获取当前比赛信息: #{inspect(game_info.betMap)}")

            # 获取用户股票持仓
            user = socket.assigns[:current_user]
            my_stocks = get_user_stocks(user)

            # 新比赛开始时清空下注状态
            {:noreply,
             assign(socket,
               betting_enabled: true,
               current_race: game_info,
               my_stocks: my_stocks,
               my_bets: %{
                 "A" => 0,
                 "B" => 0,
                 "C" => 0,
                 "D" => 0,
                 "E" => 0,
                 "F" => 0
               }
             )}

          {:error, :no_race} ->
            Logger.info("新比赛事件但没有找到比赛信息")
            {:noreply, assign(socket, current_race: nil, betting_enabled: false)}
        end

      :race_started ->
        # 比赛开始，禁止下注
        {:noreply, assign(socket, betting_enabled: false)}

      :race_ended ->
        # 比赛结束，获取最新用户积分
        user = socket.assigns[:current_user]
        points = get_user_points(user)
        recent_races = fetch_recent_races()

        {:noreply,
         socket
         |> assign(:points, points)
         |> assign(:recent_races, recent_races)}

      :force_liquidation_completed ->
        # 强制平仓完成，更新用户积分和股票持仓
        user = socket.assigns[:current_user]

        if user do
          points = Cypridina.Accounts.get_user_points(user.id)

          my_stocks = %{
            "A" => 0,
            "B" => 0,
            "C" => 0,
            "D" => 0,
            "E" => 0,
            "F" => 0
          }

          {:noreply,
           socket
           |> put_flash(:info, "系统强制平仓完成，您的股票已按市价卖出并返还积分")
           |> assign(:points, points)
           |> assign(:my_stocks, my_stocks)}
        else
          {:noreply,
           socket
           |> put_flash(:info, "系统强制平仓完成")}
        end

      :bet_placed ->
        # 有新的下注时，更新当前比赛信息（身价可能会变化）
        case RaceController.get_current_game_info() do
          {:ok, game_info} ->
            {:noreply, assign(socket, :current_race, game_info)}

          {:error, :no_race} ->
            {:noreply, assign(socket, :current_race, nil)}
        end

      _ ->
        {:noreply, socket}
    end
  end

  def handle_info({:race_force_ended, race}, socket) do
    # 比赛被强制结束，获取最新用户积分和比赛历史
    user = socket.assigns[:current_user]
    points = get_user_points(user)
    recent_races = fetch_recent_races()

    # 比赛强制结束，重新启用交易和竞猜功能
    {:noreply,
     socket
     |> put_flash(:info, "比赛已被管理员强制结束")
     |> assign(:points, points)
     |> assign(:recent_races, recent_races)
     |> assign(:betting_enabled, false)}
  end

  def handle_info({event, _}, socket) do
    Logger.info("未处理s事件 #{inspect(event)}")
    {:noreply, socket}
  end

  # 辅助函数：获取用户股票持仓
  defp get_user_stocks(nil), do: %{"A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0, "F" => 0}

  defp get_user_stocks(user) do
    stocks =
      RacingGame.Stock.get_user_stocks!(%{user_id: user.id})
      |> Enum.reduce(%{}, fn stock, acc ->
        Map.put(acc, stock.racer_id, stock.quantity)
      end)

    %{
      "A" => Map.get(stocks, "A", 0),
      "B" => Map.get(stocks, "B", 0),
      "C" => Map.get(stocks, "C", 0),
      "D" => Map.get(stocks, "D", 0),
      "E" => Map.get(stocks, "E", 0),
      "F" => Map.get(stocks, "F", 0)
    }
  end

  # 辅助函数：获取用户积分
  defp get_user_points(nil), do: 0

  defp get_user_points(user) do
    Cypridina.Accounts.get_user_points(user.id)
  end

  # 辅助函数：获取用户在当前比赛中的下注数据
  defp get_user_current_race_bets(user_id, race_issue) do
    try do
      # 获取用户在当前比赛中的所有下注记录
      bets =
        RacingGame.Bet.get_user_race_bets!(%{
          user_id: user_id,
          race_issue: race_issue
        })

      # 按选择分组并汇总金额
      bets
      |> Enum.reduce(
        %{
          "A" => 0,
          "B" => 0,
          "C" => 0,
          "D" => 0,
          "E" => 0,
          "F" => 0
        },
        fn bet, acc ->
          Map.update!(acc, bet.selection, &(&1 + bet.amount))
        end
      )
    rescue
      error ->
        Logger.error("获取用户当前比赛下注数据失败: #{inspect(error)}")

        %{
          "A" => 0,
          "B" => 0,
          "C" => 0,
          "D" => 0,
          "E" => 0,
          "F" => 0
        }
    end
  end

  def handle_event("toggle_bet_history", _params, socket) do
    user = socket.assigns[:current_user]

    socket =
      if user && !socket.assigns.show_bet_history do
        # 获取用户下注历史
        bet_history = RaceController.get_user_bets(user.id)
        assign(socket, bet_history: bet_history)
      else
        socket
      end

    {:noreply, assign(socket, show_bet_history: !socket.assigns.show_bet_history)}
  end

  def handle_event("logout", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/sign-out")}
  end

  def handle_event("to_reset_password", _params, socket) do
    # 重定向到管理后台的个人信息页面，在那里可以修改密码
    {:noreply, push_navigate(socket, to: ~p"/admin_panel")}
  end

  def handle_event("to_admin", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin_panel")}
  end

  # 显示操作弹窗（买入/卖出/竞猜）
  def handle_event("show_action_modal", %{"action" => action, "animal" => animal_id}, socket) do
    # 清除之前的flash消息，避免重复显示
    socket = socket |> clear_flash(:info) |> clear_flash(:error)

    user = socket.assigns[:current_user]

    cond do
      !user ->
        {:noreply, put_flash(socket, :error, "请先登录后再操作！")}

      # 检查比赛是否进行中（禁止下注期间）
      !socket.assigns.betting_enabled ->
        {:noreply, put_flash(socket, :error, "比赛正在进行中，暂时无法操作！")}

      # 验证卖出时是否有足够库存
      action == "sell" && Map.get(socket.assigns.my_stocks, animal_id, 0) <= 0 ->
        {:noreply, put_flash(socket, :error, "您没有持有该选手的股票，无法卖出！")}

      # 验证当前是否有比赛进行中
      is_nil(socket.assigns[:current_race]) || is_nil(socket.assigns[:current_race].betMap) ->
        {:noreply, put_flash(socket, :error, "当前没有比赛进行中，无法进行操作！")}

      true ->
        current_race = socket.assigns[:current_race]
        animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)
        # 根据操作类型设置不同的默认值
        {default_amount, max_amount} =
          case action do
            "sell" ->
              current_holding = Map.get(socket.assigns.my_stocks, animal_id, 0)
              {1, current_holding}

            "buy" ->
              max_affordable = div(socket.assigns.points, current_race.betMap[animal_id])
              {1, max_affordable}

            "bet" ->
              # 竞猜使用任意正整数，默认100，最大为用户积分
              user_points = socket.assigns.points
              {100, max(1, user_points)}
          end

        # 确保默认值不超过最大值
        default_amount = min(default_amount, max(1, max_amount))

        {:noreply,
         socket
         |> assign(:show_action_modal, true)
         |> assign(:current_action, action)
         |> assign(:current_animal_id, animal_id)
         |> assign(:current_animal_name, animal.name)
         |> assign(:action_amount, default_amount)
         |> assign(:current_price, current_race.betMap[animal_id])}
    end
  end

  # 更新操作数量
  def handle_event("update_action_amount", %{"value" => value}, socket) do
    {amount, _} = Integer.parse(value)
    {:noreply, assign(socket, update_action_amount(amount, socket))}
  end

  # 更新操作数量
  def handle_event("update_action_amount", params, socket) do
    # 从表单中获取值
    Logger.info(params, label: "收到的参数")
    value = params["action-amount"]

    if is_nil(value) || value == "" do
      {:noreply, socket}
    else
      {amount, _} = Integer.parse(value)
      {:noreply, assign(socket, update_action_amount(amount, socket))}
    end
  end

  # 统一处理数量更新的辅助函数
  defp update_action_amount(amount, socket) do
    amount = max(1, amount)
    # 根据操作类型限制最大值
    amount =
      case socket.assigns.current_action do
        "sell" ->
          max_amount = Map.get(socket.assigns.my_stocks, socket.assigns.current_animal_id, 0)
          min(amount, max_amount)

        "buy" ->
          max_affordable = div(socket.assigns.points, socket.assigns.current_price)
          min(amount, max_affordable)

        "bet" ->
          # 竞猜使用任意正整数
          amount = max(1, amount)
          max_affordable = socket.assigns.points
          min(amount, max_affordable)

        _ ->
          amount
      end

    %{action_amount: amount}
  end

  # 减少操作数量
  def handle_event("decrease_amount", _params, socket) do
    current_amount = socket.assigns.action_amount

    new_amount =
      case socket.assigns.current_action do
        "bet" ->
          # 竞猜按100递减，最小为1
          max(1, current_amount - 100)

        _ ->
          # 买入/卖出按1递减
          max(1, current_amount - 1)
      end

    {:noreply, assign(socket, update_action_amount(new_amount, socket))}
  end

  # 增加操作数量
  def handle_event("increase_amount", _params, socket) do
    current_amount = socket.assigns.action_amount

    new_amount =
      case socket.assigns.current_action do
        "bet" ->
          # 竞猜按100递增
          current_amount + 100

        _ ->
          # 买入/卖出按1递增
          current_amount + 1
      end

    {:noreply, assign(socket, update_action_amount(new_amount, socket))}
  end

  # 取消操作
  def handle_event("cancel_action", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_action_modal, false)
     |> assign(:current_action, nil)
     |> assign(:current_animal_id, nil)
     |> assign(:current_animal_name, nil)}
  end

  # 显示清仓确认弹窗
  def handle_event("show_liquidate_modal", _params, socket) do
    user = socket.assigns[:current_user]

    cond do
      !user ->
        {:noreply, put_flash(socket, :error, "请先登录后再操作！")}

      !socket.assigns.betting_enabled ->
        {:noreply, put_flash(socket, :error, "比赛正在进行中，暂时无法操作！")}

      # 检查用户是否有股票
      !has_stocks?(socket.assigns.my_stocks) ->
        {:noreply, put_flash(socket, :error, "您当前没有持有任何股票！")}

      true ->
        {:noreply, assign(socket, :show_liquidate_modal, true)}
    end
  end

  # 隐藏清仓确认弹窗
  def handle_event("hide_liquidate_modal", _params, socket) do
    {:noreply, assign(socket, :show_liquidate_modal, false)}
  end

  # 确认清仓操作
  def handle_event("confirm_liquidate", _params, socket) do
    user = socket.assigns[:current_user]

    case RacingGame.Stock.liquidate_all_stocks(%{user_id: user.id}) do
      {:ok, result} ->
        # 更新用户积分和持仓数据
        points = Cypridina.Accounts.get_user_points(user.id)
        my_stocks = %{"A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0, "F" => 0}

        {:noreply,
         socket
         |> put_flash(:info, "清仓成功！共清仓#{result.liquidated_stocks}只股票，获得#{result.total_value}积分")
         |> assign(:points, points)
         |> assign(:my_stocks, my_stocks)
         |> assign(:show_liquidate_modal, false)}

      {:error, reason} ->
        Logger.error("清仓失败: #{inspect(reason)}")

        {:noreply,
         socket
         |> put_flash(:error, "清仓失败: #{inspect(reason)}")
         |> assign(:show_liquidate_modal, false)}
    end
  end

  # 确认操作
  def handle_event(
        "confirm_action",
        %{"action" => action, "animal" => animal_id, "amount" => amount_str},
        socket
      ) do
    user = socket.assigns[:current_user]
    {amount, _} = Integer.parse(amount_str)

    current_race = RacingGame.RaceController.get_current_race()

    if current_race.status == 0 do
      socket =
        case action do
          "buy" -> handle_buy_stock(socket, user, animal_id, amount)
          "sell" -> handle_sell_stock(socket, user, animal_id, amount)
          "bet" -> handle_bet(socket, user, animal_id, amount)
          _ -> socket |> put_flash(:error, "未知操作")
        end

      {:noreply,
       socket
       |> assign(:show_action_modal, false)
       |> assign(:current_action, nil)
       |> assign(:current_animal_id, nil)
       |> assign(:current_animal_name, nil)}
    else
      {:noreply, socket |> put_flash(:error, "当前不在下注时间段")}
    end
  end

  # 处理买入股票操作
  defp handle_buy_stock(socket, user, animal_id, amount) do
    current_race =
      case RacingGame.RaceController.get_current_game_info() do
        {:ok, race} -> race
        {:error, :no_race} -> nil
      end

    animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)

    # 检查当前比赛和价格信息是否可用
    if current_race && current_race.betMap do
      # 获取当前价格和总价
      stock_price = current_race.betMap[animal_id]
      total_price = stock_price * amount

      # 检查用户是否有足够积分
      user_points = socket.assigns.points

      cond do
        user_points < total_price ->
          socket |> put_flash(:error, "积分不足，无法购买！")

        stock_price < @buy_limit_price ->
          socket |> put_flash(:error, "当前价格低于#{inspect(@buy_limit_price)}，无法购买！")

        true ->
          # 使用事务确保数据一致性
          case buy_stock_transaction(
                 user.id,
                 animal_id,
                 animal.name,
                 amount,
                 stock_price,
                 total_price,
                 current_race.issue
               ) do
            {:ok, %{quantity: quantity}} ->
              # 更新用户积分和持仓数据
              points = Cypridina.Accounts.get_user_points(user.id)
              my_stocks = socket.assigns.my_stocks |> Map.put(animal_id, quantity)

              socket
              |> put_flash(:info, "成功买入#{animal.name}的#{amount}份股票！")
              |> assign(:points, points)
              |> assign(:my_stocks, my_stocks)

            {:error, reason} ->
              Logger.error("股票买入失败: #{inspect(reason)}")
              socket |> put_flash(:error, "买入失败: #{inspect(reason)}")
          end
      end
    else
      socket |> put_flash(:error, "当前没有比赛进行中，无法买入股票！")
    end
  end

  # 处理卖出股票操作
  defp handle_sell_stock(socket, user, animal_id, amount) do
    current_race =
      case RacingGame.RaceController.get_current_game_info() do
        {:ok, race} -> race
        {:error, :no_race} -> nil
      end

    animal = socket.assigns.animals |> Enum.find(fn a -> a.id == animal_id end)

    # 从数据库获取用户最新的持仓数据，确保数据准确性
    case RacingGame.Stock.get_stock(%{user_id: user.id, racer_id: animal_id}) do
      {:ok, stock} ->
        current_holding = stock.quantity || 0

        if current_holding < amount do
          socket |> put_flash(:error, "您的#{animal.name}持仓不足，无法卖出！当前持仓：#{current_holding}份")
        else
          handle_sell_stock_with_sufficient_holding(
            socket,
            user,
            animal_id,
            amount,
            current_race,
            animal
          )
        end

      {:error, _} ->
        # 没有找到持仓记录，说明用户没有该股票
        socket |> put_flash(:error, "您没有#{animal.name}的持仓，无法卖出！")
    end
  end

  # 处理有足够持仓的卖出操作
  defp handle_sell_stock_with_sufficient_holding(
         socket,
         user,
         animal_id,
         amount,
         current_race,
         animal
       ) do
    # 获取当前价格和总价
    stock_price = current_race.betMap[animal_id]
    total_price = stock_price * amount

    # 使用事务确保数据一致性
    case sell_stock_transaction(user.id, animal_id, animal.name, amount, stock_price, total_price) do
      {:ok,
       %{actual_income: actual_income, commission_amount: commission_amount, quantity: quantity}} ->
        # 更新用户积分和持仓数据
        points = Cypridina.Accounts.get_user_points(user.id)
        my_stocks = socket.assigns.my_stocks |> Map.put(animal_id, quantity)

        commission_msg =
          if commission_amount > 0 do
            "，抽水#{commission_amount}积分"
          else
            ""
          end

        socket
        |> put_flash(
          :info,
          "成功卖出#{animal.name}的#{amount}份股票，获得#{actual_income}积分#{commission_msg}！"
        )
        |> assign(:points, points)
        |> assign(:my_stocks, my_stocks)

      {:error, reason} ->
        Logger.error("股票卖出失败: #{inspect(reason)}")
        socket |> put_flash(:error, "卖出失败: #{inspect(reason)}")
    end
  end

  # 处理竞猜操作
  defp handle_bet(socket, user, animal_id, bet_amount) do
    current_race = socket.assigns[:current_race]
    my_bets = socket.assigns[:my_bets]

    # 使用固定积分下注，bet_amount就是要下注的积分数
    total_bet_amount = bet_amount

    # 检查用户是否有足够积分
    user_points = socket.assigns.points

    if user_points < total_bet_amount do
      socket |> put_flash(:error, "积分不足，无法下注！")
    else
      case RaceController.place_bet(user.id, animal_id, total_bet_amount) do
        {:ok, _result} ->
          # 更新用户下注记录 (记录总积分)
          my_bets = my_bets |> Map.update!(animal_id, &(&1 + total_bet_amount))

          # 更新用户积分
          points = Cypridina.Accounts.get_user_points(user.id)

          socket
          |> put_flash(:info, "成功下注#{total_bet_amount}积分到#{get_animal_name(animal_id)}！")
          |> assign(:points, points)
          |> assign(:my_bets, my_bets)

        {:error, reason} ->
          Logger.error("下注失败 #{inspect(reason)}")
          socket |> put_flash(:error, "下注失败: #{inspect(reason)}")
      end
    end
  end

  # 检查用户是否有股票的辅助函数
  defp has_stocks?(my_stocks) do
    Enum.any?(my_stocks, fn {_animal_id, quantity} -> quantity > 0 end)
  end

  # 计算清仓总价值的辅助函数
  defp calculate_total_liquidation_value(my_stocks, bet_map) do
    my_stocks
    |> Enum.reduce(0, fn {animal_id, quantity}, acc ->
      if quantity > 0 do
        current_price = bet_map[animal_id] || 0
        acc + current_price * quantity
      else
        acc
      end
    end)
  end

  # 根据动物ID获取动物名称的辅助函数
  defp get_animal_name(animal_id) do
    animals = [
      %{id: "A", name: "饿小宝"},
      %{id: "B", name: "盒马"},
      %{id: "C", name: "票票"},
      %{id: "D", name: "虾仔"},
      %{id: "E", name: "支小宝"},
      %{id: "F", name: "欢猩"}
    ]

    animal = Enum.find(animals, fn animal -> animal.id == animal_id end)
    if animal, do: animal.name, else: "未知动物"
  end

  # 助手函数，为动物添加索引属性，用于K线图
  defp add_index_to_animals(animals) do
    animals
    |> Enum.with_index(1)
    |> Enum.map(fn {animal, index} ->
      Map.put(animal, :index, index)
    end)
  end

  # 获取最近5场比赛数据（用于排名走势图）
  defp fetch_recent_races() do
    races =
      RaceController.get_recent_race_results()
      |> Enum.map(fn race ->
        # 从positions数组中提取排名信息
        positions = race.positions || []

        %{
          issue: race.issue,
          bet_amount_map: race.bet_amount_map,
          positions: positions,
          # 为了兼容图表代码，添加具体的排名字段
          champion: Enum.at(positions, 0),
          second: Enum.at(positions, 1),
          third: Enum.at(positions, 2),
          fourth: Enum.at(positions, 3),
          fifth: Enum.at(positions, 4),
          sixth: Enum.at(positions, 5)
        }
      end)

    races
    |> Enum.take(10)
    |> Enum.reverse()
  end

  # 防止表单默认提交
  def handle_event("prevent_default", _params, socket) do
    {:noreply, socket}
  end

  # 计算抽水后的实际收入和抽水金额
  defp calculate_commission_and_income(user_id, total_amount) do
    case Cypridina.Accounts.get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        # 计算抽水金额（向下取整）
        commission_amount =
          total_amount
          |> Decimal.mult(agent_relationship.commission_rate)
          # 向下取整到整数
          |> Decimal.round(0, :down)
          |> Decimal.to_integer()

        # 实际收入 = 总金额 - 抽水金额
        actual_income = total_amount - commission_amount

        {actual_income, commission_amount}

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        {total_amount, 0}

      {:error, _reason} ->
        # 获取代理关系失败，不抽水
        {total_amount, 0}
    end
  end

  # 计算抽水后的实际收入、抽水金额和抽水比例
  defp calculate_commission_and_income_with_rate(user_id, total_amount) do
    case Cypridina.Accounts.get_user_agent_relationship(user_id) do
      {:ok, agent_relationship} ->
        # 计算抽水金额（向下取整）
        commission_amount =
          total_amount
          |> Decimal.mult(agent_relationship.commission_rate)
          # 向下取整到整数
          |> Decimal.round(0, :down)
          |> Decimal.to_integer()

        # 实际收入 = 总金额 - 抽水金额
        actual_income = total_amount - commission_amount

        {actual_income, commission_amount, agent_relationship.commission_rate}

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        {total_amount, 0, Decimal.new(0)}

      {:error, _reason} ->
        # 获取代理关系失败，不抽水
        {total_amount, 0, Decimal.new(0)}
    end
  end

  # 事务化的股票买入操作
  defp buy_stock_transaction(
         user_id,
         animal_id,
         animal_name,
         amount,
         stock_price,
         total_price,
         race_issue
       ) do
    Cypridina.Repo.transaction(fn ->
      # 1. 扣除用户积分
      case Cypridina.Accounts.subtract_points(user_id, total_price,
             transaction_type: :buy_stock,
             description: "股票买入: #{animal_name}",
             metadata: %{
               racer_id: animal_id,
               racer_name: animal_name,
               quantity: amount,
               stock_price: stock_price,
               total_cost: total_price,
               race_issue: race_issue,
               transaction_target_id: "-1",
               transaction_target_type: :system
             }
           ) do
        {:ok, _new_points} ->
          # 2. 增加用户的股票持仓 - 使用 return_notifications?: true
          case Ash.create!(
                 RacingGame.Stock,
                 %{
                   user_id: user_id,
                   racer_id: animal_id,
                   amount: amount,
                   cost: Decimal.new(total_price),
                   # 系统操作
                   transaction_target_id: "-1",
                   transaction_target_type: :system
                 },
                 action: :add,
                 return_notifications?: true
               ) do
            {%{quantity: quantity} = stock, notifications} ->
              Logger.info(
                "股票买入事务成功: 用户 #{user_id}, 动物 #{animal_id}, 数量 #{amount}, 总价 #{total_price}"
              )

              # 在事务外发送通知
              Task.start(fn ->
                Ash.Notifier.notify(notifications)
              end)

              stock

            error ->
              Logger.error("股票持仓增加失败: #{inspect(error)}")
              Cypridina.Repo.rollback("股票持仓增加失败")
          end

        {:error, reason} ->
          Logger.error("积分扣除失败: #{inspect(reason)}")
          Cypridina.Repo.rollback("积分扣除失败: #{inspect(reason)}")
      end
    end)
  end

  # 事务化的股票卖出操作
  defp sell_stock_transaction(user_id, animal_id, animal_name, amount, stock_price, total_price) do
    Cypridina.Repo.transaction(fn ->
      # 1. 减少用户的股票持仓 - 使用 return_notifications?: true
      case Ash.create!(
             RacingGame.Stock,
             %{
               user_id: user_id,
               racer_id: animal_id,
               amount: amount,
               # 系统操作
               transaction_target_id: "-1",
               transaction_target_type: :system
             },
             action: :subtract,
             return_notifications?: true
           ) do
        {%{quantity: quantity}, notifications} ->
          # 在事务外发送通知
          Task.start(fn ->
            Ash.Notifier.notify(notifications)
          end)

          # 2. 计算抽水后的实际收入
          {actual_income, commission_amount, commission_rate} =
            calculate_commission_and_income_with_rate(user_id, total_price)

          # 3. 给用户增加积分（扣除抽水后的金额）
          case Cypridina.Accounts.add_points(user_id, actual_income,
                 transaction_type: :sell_stock,
                 description: "卖出股票: #{animal_name}",
                 metadata: %{
                   racer_id: animal_id,
                   racer_name: animal_name,
                   quantity: amount,
                   stock_price: stock_price,
                   total_income: actual_income,
                   original_amount: total_price,
                   commission_amount: commission_amount,
                   racing_game: true
                 }
               ) do
            {ok, _new_balance} ->
              # 4. 处理代理抽水（如果有）
              if commission_amount > 0 do
                case Cypridina.Accounts.get_user_agent_relationship(user_id) do
                  {:ok, agent_relationship} ->
                    agent_id = agent_relationship.agent_id

                    case Cypridina.Accounts.add_points(agent_id, commission_amount,
                           transaction_type: :commission,
                           description: "股票抽水: #{animal_name}",
                           metadata: %{
                             commission_type: "stock",
                             racer_id: animal_id,
                             racer_name: animal_name,
                             quantity: amount,
                             stock_price: stock_price,
                             original_amount: total_price,
                             commission_rate: commission_rate,
                             source_user_id: user_id,
                             racing_game: true
                           }
                         ) do
                      {:ok, _agent_balance} ->
                        Logger.info(
                          "股票卖出事务成功: 用户 #{user_id}, 动物 #{animal_id}, 数量 #{amount}, 收入 #{actual_income}, 抽水 #{commission_amount}"
                        )

                        %{
                          actual_income: actual_income,
                          commission_amount: commission_amount,
                          quantity: quantity
                        }

                      {:error, reason} ->
                        Logger.error("代理抽水失败: #{inspect(reason)}")
                        Cypridina.Repo.rollback("代理抽水失败: #{inspect(reason)}")
                    end

                  {:error, :no_agent} ->
                    # 用户没有代理，不需要抽水

                    Logger.info(
                      "股票卖出事务成功: 用户 #{user_id}, 动物 #{animal_id}, 数量 #{amount}, 收入 #{actual_income}"
                    )

                    %{
                      actual_income: actual_income,
                      commission_amount: commission_amount,
                      quantity: quantity
                    }

                  {:error, reason} ->
                    Logger.error("获取代理关系失败: #{inspect(reason)}")
                    Cypridina.Repo.rollback("获取代理关系失败: #{inspect(reason)}")
                end
              else
                # 没有抽水

                Logger.info(
                  "股票卖出事务成功: 用户 #{user_id}, 动物 #{animal_id}, 数量 #{amount}, 收入 #{actual_income}"
                )

                %{
                  actual_income: actual_income,
                  commission_amount: commission_amount,
                  quantity: quantity
                }
              end

            {:error, reason} ->
              Logger.error("积分增加失败: #{inspect(reason)}")
              Cypridina.Repo.rollback("积分增加失败: #{inspect(reason)}")
          end

        error ->
          Logger.error("股票持仓减少失败: #{inspect(error)}")
          Cypridina.Repo.rollback("股票持仓减少失败")
      end
    end)
  end
end
